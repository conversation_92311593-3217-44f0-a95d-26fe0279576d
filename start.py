#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI停车场管理系统启动脚本
"""

import os
import sys

def check_requirements():
    """检查必要的依赖"""
    try:
        import cv2
        import numpy
        import pytesseract
        import flask
        import sqlalchemy
        print("✓ 所有必要依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_tesseract():
    """检查Tesseract OCR"""
    tesseract_path = r'E:\Tesseract OCR\tesseract.exe'
    if os.path.exists(tesseract_path):
        print("✓ Tesseract OCR 已找到")
        return True
    else:
        print("❌ Tesseract OCR 未找到")
        print("请安装 Tesseract OCR 并确保路径正确")
        return False

def init_database():
    """初始化数据库"""
    try:
        from database.db_manager import init_db
        init_db()
        print("✓ 数据库初始化成功")
        return True
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = ['uploads', 'static/img']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("✓ 目录结构创建完成")

def main():
    """主函数"""
    print("🚗 AI停车场管理系统启动检查")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        return False
    
    # 检查Tesseract
    if not check_tesseract():
        print("⚠️ 车牌识别功能可能无法正常工作")
    
    # 创建目录
    create_directories()
    
    # 初始化数据库
    if not init_database():
        return False
    
    print("\n🎉 系统检查完成，准备启动...")
    print("=" * 50)
    
    # 启动应用
    try:
        from app import app
        print("🌐 启动Web服务器...")
        print("📱 请在浏览器中访问: http://localhost:5000")
        print("👤 默认用户名: admin, 密码: admin123")
        print("\n按 Ctrl+C 停止服务器")
        
        app.run(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
