#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的车牌识别测试
"""

import os

def test_with_image(image_path):
    """测试指定图像的车牌识别"""
    print(f"测试图像: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"错误: 图像文件不存在")
        return
    
    try:
        # 1. 配置 Tesseract
        import pytesseract
        pytesseract.pytesseract.tesseract_cmd = r'E:\Tesseract OCR\tesseract.exe'
        print("✓ Tesseract 路径已设置")
        
        # 2. 导入识别模块
        from recognition.plate_recognizer import recognize_plate_from_image
        print("✓ 车牌识别模块已导入")
        
        # 3. 进行识别（跳过车辆检测）
        print("开始识别...")
        result = recognize_plate_from_image(image_path, skip_vehicle_detection=True)
        
        if result:
            print(f"✓ 识别成功: {result}")
        else:
            print("✗ 识别失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    # 测试 uploads 目录中的图像
    uploads_dir = "uploads"
    if os.path.exists(uploads_dir):
        image_files = [f for f in os.listdir(uploads_dir) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
        
        if image_files:
            for image_file in image_files:
                image_path = os.path.join(uploads_dir, image_file)
                test_with_image(image_path)
                print("-" * 50)
        else:
            print("uploads 目录中没有找到图像文件")
    else:
        print("uploads 目录不存在")
