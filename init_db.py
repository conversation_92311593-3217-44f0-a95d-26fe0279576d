#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库初始化脚本
用于创建MySQL数据库和表结构
"""

import sys
from database.db_manager import init_db, session_scope
from database.models import User, ParkingFee, MembershipType

def create_default_data():
    """创建默认数据"""
    try:
        with session_scope() as session:
            # 检查是否已有管理员用户
            admin = session.query(User).filter_by(username='admin').first()
            if not admin:
                print("创建默认管理员用户...")
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True
                )
                admin.set_password('admin123')
                session.add(admin)
                print("默认管理员用户创建成功")

            # 检查是否已有默认停车费率
            default_fee = session.query(ParkingFee).first()
            if not default_fee:
                print("创建默认停车费率...")
                default_fee = ParkingFee(
                    hourly_rate=10.0,
                    daily_max=100.0,
                    free_minutes=15,
                    description='默认停车费率'
                )
                session.add(default_fee)
                print("默认停车费率创建成功")

            # 检查是否已有默认会员类型
            membership_types = [
                ('普通会员', 0.9, 50.0, '享受9折优惠'),
                ('VIP会员', 0.8, 100.0, '享受8折优惠'),
                ('企业会员', 0.7, 200.0, '享受7折优惠')
            ]

            for name, discount, fee, desc in membership_types:
                existing = session.query(MembershipType).filter_by(name=name).first()
                if not existing:
                    print(f"创建会员类型: {name}...")
                    membership_type = MembershipType(
                        name=name,
                        discount_rate=discount,
                        monthly_fee=fee,
                        description=desc
                    )
                    session.add(membership_type)
                    print(f"会员类型 {name} 创建成功")

            print("默认数据创建完成")
    except Exception as e:
        print(f"创建默认数据时出错: {str(e)}")
        raise

if __name__ == '__main__':
    print("开始初始化数据库...")
    try:
        # 初始化数据库表结构
        init_db()

        # 创建默认数据
        create_default_data()

        print("数据库初始化成功!")
    except Exception as e:
        print(f"数据库初始化失败: {str(e)}")
        sys.exit(1)
