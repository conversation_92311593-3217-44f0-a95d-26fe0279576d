#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
车牌识别模块
"""

import os
import cv2
import numpy as np
import pytesseract

# 设置Tesseract路径（如果需要）
pytesseract.pytesseract.tesseract_cmd = r'E:\Tesseract OCR\tesseract.exe'

# 改进车牌识别预处理
def preprocess_image(image_path):
    """
    预处理图像以便更好地识别车牌

    Args:
        image_path: 图像文件路径

    Returns:
        处理后的图像
    """
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        return None, None

    # 保存原始图像用于调试
    original_img = img.copy()

    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 高斯模糊，减少噪声
    blur = cv2.GaussianBlur(gray, (5, 5), 0)

    # 自适应直方图均衡化
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    equalized = clahe.apply(blur)

    # 特别处理蓝色车牌 - 提取蓝色区域
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    # 蓝色范围 (中国蓝牌)
    lower_blue = np.array([100, 80, 80])
    upper_blue = np.array([130, 255, 255])
    blue_mask = cv2.inRange(hsv, lower_blue, upper_blue)

    # 形态学操作改进蓝色掩码
    kernel = np.ones((3, 3), np.uint8)
    blue_mask = cv2.morphologyEx(blue_mask, cv2.MORPH_CLOSE, kernel)
    blue_mask = cv2.morphologyEx(blue_mask, cv2.MORPH_OPEN, kernel)

    # 边缘检测
    edges = cv2.Canny(equalized, 100, 200)

    # 结合蓝色掩码和边缘
    combined_mask = cv2.bitwise_or(edges, blue_mask)

    # 膨胀和腐蚀操作，闭运算，消除噪点和小区域
    kernel = np.ones((3, 3), np.uint8)
    dilation = cv2.dilate(combined_mask, kernel, iterations=2)
    processed_mask = cv2.erode(dilation, kernel, iterations=1)

    # 添加自适应阈值处理
    adaptive_thresh = cv2.adaptiveThreshold(
        equalized, 255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV, 15, 2
    )

    # 添加形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    morph = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)

    # 去除小区域噪声
    contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours:
        if cv2.contourArea(contour) < 100:  # 面积阈值
            cv2.drawContours(morph, [contour], -1, 0, -1)

    # 使用形态学处理后的结果
    result1 = morph

    # 保存处理后的图像用于调试
    debug_dir = os.path.join(os.path.dirname(image_path), 'debug')
    os.makedirs(debug_dir, exist_ok=True)
    cv2.imwrite(os.path.join(debug_dir, 'gray.jpg'), gray)
    cv2.imwrite(os.path.join(debug_dir, 'equalized.jpg'), equalized)
    cv2.imwrite(os.path.join(debug_dir, 'blue_mask.jpg'), blue_mask)
    cv2.imwrite(os.path.join(debug_dir, 'edges.jpg'), edges)
    cv2.imwrite(os.path.join(debug_dir, 'combined_mask.jpg'), combined_mask)
    cv2.imwrite(os.path.join(debug_dir, 'morph.jpg'), morph)
    cv2.imwrite(os.path.join(debug_dir, 'processed_mask.jpg'), processed_mask)

    return result1, original_img

def detect_plate_regions(processed_img, original_img):
    """
    检测图像中的车牌区域

    Args:
        processed_img: 预处理后的图像
        original_img: 原始图像

    Returns:
        可能的车牌区域列表
    """
    if processed_img is None or original_img is None:
        print("预处理图像或原始图像为空")
        return []

    # 查找轮廓
    contours, _ = cv2.findContours(processed_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if len(contours) == 0:
        print("未检测到任何轮廓")
        return []

    # 筛选可能的车牌区域
    plate_candidates = []
    height, width = original_img.shape[:2]

    # 调整面积阈值 - 更宽松的范围
    min_area = 200  # 绝对最小面积
    max_area = width * height * 0.3     # 最大面积

    # 记录筛选过程
    total_contours = len(contours)
    area_filtered = 0
    ratio_filtered = 0
    extent_filtered = 0
    edge_filtered = 0
    color_filtered = 0
    passed_filters = 0

    for contour in contours:
        # 计算轮廓面积
        area = cv2.contourArea(contour)

        # 面积筛选
        if area < min_area or area > max_area:
            area_filtered += 1
            continue

        # 获取矩形边界
        x, y, w, h = cv2.boundingRect(contour)

        # 计算宽高比，中国车牌宽高比约为3:1，但考虑拍摄角度
        aspect_ratio = float(w) / h

        # 宽高比筛选 - 车牌应该明显比高度宽
        if not (1.5 < aspect_ratio < 8.0):  # 放宽宽高比范围
            ratio_filtered += 1
            continue

        # 计算矩形度 - 降低要求
        rect_area = w * h
        extent = float(area) / rect_area
        if extent <= 0.2:  # 大幅降低矩形度要求
            extent_filtered += 1
            continue

        # 检查区域是否靠近图像边缘
        margin = 5  # 减小边缘距离
        if not (x > margin and y > margin and x + w < width - margin and y + h < height - margin):
            edge_filtered += 1
            continue

        # 基本尺寸检查 - 车牌应该有合理的尺寸
        if w < 30 or h < 10 or w > 500 or h > 150:
            continue

        # 提取候选区域进行进一步分析
        candidate_img = original_img[y:y+h, x:x+w]

        # 颜色特征分析 - 使用更严格的颜色检查
        if not analyze_plate_color_strict(candidate_img):
            color_filtered += 1
            continue

        # 通过所有筛选条件
        passed_filters += 1
        plate_candidates.append({
            'bbox': (x, y, w, h),
            'area': area,
            'aspect_ratio': aspect_ratio,
            'extent': extent,
            'score': calculate_plate_score(candidate_img, aspect_ratio, extent)
        })

    # 打印筛选统计信息
    print(f"轮廓总数: {total_contours}, 通过筛选: {passed_filters}")
    print(f"筛选详情 - 面积: {area_filtered}, 宽高比: {ratio_filtered}, 矩形度: {extent_filtered}, 边缘: {edge_filtered}, 颜色: {color_filtered}")

    # 如果没有找到符合条件的区域
    if not plate_candidates:
        print("未找到符合车牌特征的区域")
        return []

    # 按评分排序，选择最可能的车牌区域
    plate_candidates.sort(key=lambda x: x['score'], reverse=True)

    # 提取最佳候选区域
    plate_images = []
    for candidate in plate_candidates[:5]:  # 最多取前5个最佳候选
        x, y, w, h = candidate['bbox']

        # 适当扩大提取区域
        expand_x = int(w * 0.1)
        expand_y = int(h * 0.2)
        x1 = max(0, x - expand_x)
        y1 = max(0, y - expand_y)
        x2 = min(width, x + w + expand_x)
        y2 = min(height, y + h + expand_y)

        plate_img = original_img[y1:y2, x1:x2]
        if plate_img.size > 0:
            plate_images.append(plate_img)
            print(f"车牌候选区域: 位置({x},{y}), 尺寸({w}x{h}), 宽高比: {candidate['aspect_ratio']:.2f}, 评分: {candidate['score']:.2f}")

    return plate_images

def analyze_plate_color(img):
    """
    分析图像是否具有车牌的颜色特征

    Args:
        img: 候选车牌图像

    Returns:
        bool: 是否具有车牌颜色特征
    """
    if img is None or img.size == 0:
        return False

    # 转换到HSV颜色空间
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

    # 定义车牌颜色范围
    # 蓝色车牌
    blue_lower = np.array([100, 50, 50])
    blue_upper = np.array([130, 255, 255])
    blue_mask = cv2.inRange(hsv, blue_lower, blue_upper)

    # 黄色车牌
    yellow_lower = np.array([15, 50, 50])
    yellow_upper = np.array([35, 255, 255])
    yellow_mask = cv2.inRange(hsv, yellow_lower, yellow_upper)

    # 绿色车牌
    green_lower = np.array([35, 50, 50])
    green_upper = np.array([85, 255, 255])
    green_mask = cv2.inRange(hsv, green_lower, green_upper)

    # 白色车牌（通过亮度判断）
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    white_mask = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)[1]

    # 计算各种颜色的占比
    total_pixels = img.shape[0] * img.shape[1]
    blue_ratio = np.sum(blue_mask > 0) / total_pixels
    yellow_ratio = np.sum(yellow_mask > 0) / total_pixels
    green_ratio = np.sum(green_mask > 0) / total_pixels
    white_ratio = np.sum(white_mask > 0) / total_pixels

    # 如果任何一种车牌颜色占比超过阈值，认为可能是车牌
    color_threshold = 0.1  # 10%的像素符合车牌颜色

    if (blue_ratio > color_threshold or yellow_ratio > color_threshold or
        green_ratio > color_threshold or white_ratio > color_threshold):
        return True

    # 检查颜色对比度 - 车牌通常有明显的前景背景对比
    contrast = np.std(gray)
    if contrast > 30:  # 有足够的对比度
        return True

    return False

def analyze_plate_color_strict(img):
    """
    更严格的车牌颜色特征分析

    Args:
        img: 候选车牌图像

    Returns:
        bool: 是否具有车牌颜色特征
    """
    if img is None or img.size == 0:
        return False

    # 转换到HSV颜色空间
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 检查图像对比度 - 车牌应该有明显的文字和背景对比
    contrast = np.std(gray)
    if contrast < 20:  # 对比度太低
        return False

    # 检查亮度分布 - 车牌应该有合理的亮度分布
    brightness = np.mean(gray)
    if brightness < 30 or brightness > 220:  # 太暗或太亮
        return False

    # 定义更严格的车牌颜色范围
    # 蓝色车牌 (更精确的蓝色范围)
    blue_lower = np.array([100, 80, 80])
    blue_upper = np.array([125, 255, 255])
    blue_mask = cv2.inRange(hsv, blue_lower, blue_upper)

    # 黄色车牌 (更精确的黄色范围)
    yellow_lower = np.array([20, 100, 100])
    yellow_upper = np.array([30, 255, 255])
    yellow_mask = cv2.inRange(hsv, yellow_lower, yellow_upper)

    # 绿色车牌 (新能源车牌)
    green_lower = np.array([40, 80, 80])
    green_upper = np.array([80, 255, 255])
    green_mask = cv2.inRange(hsv, green_lower, green_upper)

    # 白色车牌 (通过亮度和饱和度判断)
    white_mask = cv2.inRange(hsv, np.array([0, 0, 180]), np.array([180, 30, 255]))

    # 计算各种颜色的占比
    total_pixels = img.shape[0] * img.shape[1]
    blue_ratio = np.sum(blue_mask > 0) / total_pixels
    yellow_ratio = np.sum(yellow_mask > 0) / total_pixels
    green_ratio = np.sum(green_mask > 0) / total_pixels
    white_ratio = np.sum(white_mask > 0) / total_pixels

    # 提高颜色阈值要求
    color_threshold = 0.15  # 15%的像素符合车牌颜色

    # 检查是否有足够的车牌颜色
    has_plate_color = (blue_ratio > color_threshold or
                      yellow_ratio > color_threshold or
                      green_ratio > color_threshold or
                      white_ratio > color_threshold)

    if not has_plate_color:
        return False

    # 检查边缘密度 - 车牌应该有清晰的边缘（文字边缘）
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.sum(edges > 0) / total_pixels

    if edge_density < 0.05:  # 边缘密度太低
        return False

    return True

def calculate_plate_score(img, aspect_ratio, extent):
    """
    计算车牌候选区域的评分

    Args:
        img: 候选车牌图像
        aspect_ratio: 宽高比
        extent: 矩形度

    Returns:
        float: 评分（越高越可能是车牌）
    """
    score = 0.0

    # 宽高比评分 (理想值约为3.0)
    ideal_ratio = 3.0
    ratio_diff = abs(aspect_ratio - ideal_ratio)
    ratio_score = max(0, 1.0 - ratio_diff / 2.0)  # 差异越小分数越高
    score += ratio_score * 0.3

    # 矩形度评分
    extent_score = extent  # 越接近矩形分数越高
    score += extent_score * 0.2

    # 颜色特征评分
    if analyze_plate_color(img):
        score += 0.3

    # 尺寸评分 - 车牌应该有合适的尺寸
    h, w = img.shape[:2]
    if 20 <= h <= 100 and 60 <= w <= 300:
        score += 0.2

    return score

def enhance_plate_image(plate_img):
    """
    增强车牌图像质量，提高OCR识别率

    Args:
        plate_img: 车牌图像

    Returns:
        增强后的图像列表
    """
    if plate_img is None or plate_img.size == 0:
        return []

    # 转换为灰度图
    gray = cv2.cvtColor(plate_img, cv2.COLOR_BGR2GRAY)

    # 图像尺寸标准化 - 放大到合适尺寸
    height, width = gray.shape
    target_height = 64  # 目标高度
    scale = target_height / height
    new_width = int(width * scale)
    resized = cv2.resize(gray, (new_width, target_height), interpolation=cv2.INTER_CUBIC)

    enhanced_images = []

    # 方法1: 高斯模糊 + 自适应阈值
    blurred = cv2.GaussianBlur(resized, (3, 3), 0)
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
    equalized = clahe.apply(blurred)

    # 自适应阈值 - 多种参数组合
    adaptive1 = cv2.adaptiveThreshold(equalized, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    adaptive2 = cv2.adaptiveThreshold(equalized, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 15, 3)

    enhanced_images.extend([adaptive1, adaptive2])

    # 方法2: Otsu阈值
    _, otsu = cv2.threshold(equalized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    enhanced_images.append(otsu)

    # 方法3: 形态学操作增强
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
    morph1 = cv2.morphologyEx(adaptive1, cv2.MORPH_CLOSE, kernel)
    morph2 = cv2.morphologyEx(otsu, cv2.MORPH_OPEN, kernel)

    enhanced_images.extend([morph1, morph2])

    # 方法4: 边缘增强
    kernel_sharpen = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(equalized, -1, kernel_sharpen)
    _, sharp_binary = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    enhanced_images.append(sharp_binary)

    return enhanced_images

def recognize_text_from_plate(plate_img):
    """
    从车牌图像中识别文本

    Args:
        plate_img: 车牌图像

    Returns:
        识别出的车牌号，如果识别失败返回None
    """
    if plate_img is None or plate_img.size == 0:
        print("车牌图像无效或为空")
        return None

    # 检查图像尺寸是否合理（车牌应该有一定的尺寸）
    height, width = plate_img.shape[:2]
    min_width, min_height = 40, 15  # 降低最小尺寸要求
    if width < min_width or height < min_height:
        print(f"车牌图像尺寸过小: {width}x{height}，最小要求: {min_width}x{min_height}")
        return None

    # 转换为灰度图
    gray = cv2.cvtColor(plate_img, cv2.COLOR_BGR2GRAY)

    # 降低清晰度要求，因为有些车牌图像可能本身就不够清晰
    laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
    min_laplacian = 50  # 降低清晰度阈值
    if laplacian_var < min_laplacian:
        print(f"车牌图像清晰度较低: {laplacian_var:.2f}，但继续尝试识别")

    # 获取增强后的图像
    enhanced_images = enhance_plate_image(plate_img)

    if not enhanced_images:
        print("图像增强失败")
        return None

    # 尝试多种OCR配置
    ocr_configs = [
        '--psm 8 --oem 3',  # 单词识别
        '--psm 7 --oem 3',  # 单行文本
        '--psm 6 --oem 3',  # 单个文本块
        '--psm 13 --oem 3', # 原始行，不进行hack
    ]

    # 字符白名单
    valid_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼'

    results = []

    # 对每种增强图像和OCR配置进行识别
    for img_idx, enhanced_img in enumerate(enhanced_images):
        # 保存调试图像
        debug_dir = os.path.join(os.getcwd(), 'debug')
        os.makedirs(debug_dir, exist_ok=True)
        cv2.imwrite(os.path.join(debug_dir, f'enhanced_{img_idx+1}.jpg'), enhanced_img)

        for config_idx, config in enumerate(ocr_configs):
            try:
                # 使用中英文混合识别
                text = pytesseract.image_to_string(enhanced_img, lang='chi_sim+eng', config=config)

                # 清理识别结果
                text = text.strip().replace(' ', '').replace('\n', '').replace('\t', '')
                # 只保留有效字符
                cleaned_text = ''.join(c for c in text if c in valid_chars)

                if cleaned_text:
                    print(f"OCR结果[图像{img_idx+1}-配置{config_idx+1}]: '{cleaned_text}'")

                    # 如果识别结果长度合理，添加到结果列表
                    if 5 <= len(cleaned_text) <= 9:  # 放宽长度要求
                        results.append(cleaned_text)

            except Exception as e:
                print(f"OCR识别出错[图像{img_idx+1}-配置{config_idx+1}]: {str(e)}")
                continue

    # 如果没有有效结果，尝试更宽松的识别
    if not results:
        print("标准OCR未获得结果，尝试更宽松的识别...")

        # 使用最简单的配置再试一次
        for enhanced_img in enhanced_images[:3]:  # 只用前3个最好的增强图像
            try:
                text = pytesseract.image_to_string(enhanced_img, lang='chi_sim+eng', config='--psm 8')
                text = text.strip().replace(' ', '').replace('\n', '')

                # 更宽松的字符过滤
                filtered_text = ''
                for c in text:
                    if c in valid_chars:
                        filtered_text += c
                    elif c.isalnum():  # 允许其他字母数字字符
                        filtered_text += c

                if filtered_text and len(filtered_text) >= 4:
                    print(f"宽松OCR结果: '{filtered_text}'")
                    results.append(filtered_text)

            except Exception as e:
                continue

    # 如果仍然没有结果
    if not results:
        print("未能从车牌图像中识别出任何文本")
        return None

    # 返回最可能的结果
    from collections import Counter

    # 优先选择长度为7或8的结果
    valid_length_results = [r for r in results if len(r) in [7, 8]]
    if valid_length_results:
        most_common = Counter(valid_length_results).most_common(1)
        if most_common:
            return most_common[0][0]
        return valid_length_results[0]

    # 如果没有标准长度的结果，返回最常见的结果
    most_common = Counter(results).most_common(1)
    if most_common:
        return most_common[0][0]

    return results[0]

def validate_plate_number(plate_number):
    """
    验证车牌号格式是否正确，支持特殊字符如点和中文间隔符

    Args:
        plate_number: 识别出的车牌号

    Returns:
        验证后的车牌号，如果格式不正确则返回None
    """
    if not plate_number:
        return None

    # 移除所有空白字符
    plate_number = ''.join(plate_number.split())

    # 处理特殊字符，如点和中文间隔符
    # 允许的特殊字符：点(.)、中点(·)、中文间隔号(·)
    special_chars = ['.', '·', '•', '・']

    # 标准化特殊字符为中文间隔号
    for char in special_chars:
        if char in plate_number:
            parts = plate_number.split(char)
            if len(parts) == 2:
                # 确保分隔符在第一个字符（省份）和第二个字符（字母）之间
                if len(parts[0]) == 1 and len(parts[1]) >= 1:
                    # 重新组合，使用标准中文间隔号
                    plate_number = parts[0] + '·' + parts[1]
                    break

    # 如果包含中文间隔号，特殊处理
    if '·' in plate_number:
        parts = plate_number.split('·')
        if len(parts) == 2:
            # 验证第一部分是否为省份简称
            provinces = '京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼'
            if parts[0] not in provinces:
                print(f"车牌首字符不是有效的省份简称: {parts[0]}")
                return None

            # 验证第二部分的第一个字符是否为字母
            if not parts[1] or not parts[1][0].isalpha():
                print(f"车牌分隔符后的第一个字符不是字母: {parts[1][0] if parts[1] else '无'}")
                return None

            # 验证第二部分的剩余字符是否为字母或数字
            remaining_chars = parts[1][1:]
            if not all(c.isalnum() for c in remaining_chars):
                print(f"车牌包含非法字符: {plate_number}")
                return None

            # 验证总长度（不包括间隔符）
            total_length = len(parts[0]) + len(parts[1])
            if total_length < 7 or total_length > 8:
                print(f"车牌长度不符合规范: {total_length}，应为7-8个字符")
                return None

            # 验证通过，返回原始格式（包含间隔符）
            print(f"车牌验证通过: {plate_number}")
            return plate_number

    # 常规验证（无特殊字符的情况）
    # 验证长度：中国车牌通常为7-8个字符
    if len(plate_number) < 7 or len(plate_number) > 8:
        print(f"车牌长度不符合规范: {len(plate_number)}，应为7-8个字符")
        return None

    # 验证第一个字符必须是省份简称
    provinces = '京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼'
    if plate_number[0] not in provinces:
        print(f"车牌首字符不是有效的省份简称: {plate_number[0]}")
        return None

    # 验证第二个字符必须是字母
    if not plate_number[1].isalpha():
        print(f"车牌第二个字符不是字母: {plate_number[1]}")
        return None

    # 验证剩余字符必须是字母或数字
    remaining_chars = plate_number[2:]
    if not all(c.isalnum() for c in remaining_chars):
        print(f"车牌包含非法字符: {plate_number}")
        return None

    # 验证特殊车牌格式
    if len(plate_number) == 8:
        # 新能源车牌规则：最后一位必须是数字
        if not plate_number[-1].isdigit():
            print(f"新能源车牌最后一位必须是数字: {plate_number}")
            return None

    # 验证常规车牌格式（7位）
    elif len(plate_number) == 7:
        # 验证后5位中至少有一位是数字
        if not any(c.isdigit() for c in plate_number[2:]):
            print(f"常规车牌后5位中至少应有一位数字: {plate_number}")
            return None

    # 检查是否有明显不合理的字符组合
    suspicious_patterns = ['000000', '111111', '123456', 'AAAAAA', 'ABCDEF']
    for pattern in suspicious_patterns:
        if pattern in plate_number[2:]:
            print(f"车牌包含可疑的字符模式: {plate_number}")
            return None

    print(f"车牌验证通过: {plate_number}")
    return plate_number

def recognize_plate_from_image(image_path, skip_vehicle_detection=False):
    """
    从图像中识别车牌号 - 改进版本

    Args:
        image_path: 图像文件路径
        skip_vehicle_detection: 是否跳过车辆检测

    Returns:
        识别出的车牌号，如果未识别到则返回None
    """
    try:
        # 设置Tesseract路径
        import pytesseract
        pytesseract.pytesseract.tesseract_cmd = r'E:\Tesseract OCR\tesseract.exe'

        print(f"开始识别车牌: {image_path}")

        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            print("无法读取图像")
            return None

        print(f"图像尺寸: {img.shape}")

        # 方法1: 尝试直接识别整个图像
        result = try_direct_recognition(img)
        if result:
            print(f"直接识别成功: {result}")
            return result

        # 方法2: 尝试区域分割识别
        result = try_region_recognition(img)
        if result:
            print(f"区域识别成功: {result}")
            return result

        # 方法3: 尝试传统的轮廓检测方法
        result = try_contour_recognition(img)
        if result:
            print(f"轮廓识别成功: {result}")
            return result

        print("所有识别方法都未能找到有效车牌")
        return None

    except Exception as e:
        import traceback
        print(f"车牌识别出错: {str(e)}")
        print(traceback.format_exc())
        return None

def try_direct_recognition(img):
    """
    尝试直接识别整个图像
    """
    print("尝试直接识别...")

    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 多种预处理方法
    methods = []

    # 方法1: 自适应阈值
    adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    methods.append(adaptive)

    # 方法2: OTSU二值化
    _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    methods.append(otsu)

    # 方法3: 直方图均衡化
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    equalized = clahe.apply(gray)
    _, eq_binary = cv2.threshold(equalized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    methods.append(eq_binary)

    # 对每种方法进行OCR
    for i, processed in enumerate(methods):
        try:
            # 使用专门的车牌识别配置
            config = '--psm 8 --oem 3 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼'
            text = pytesseract.image_to_string(processed, lang='chi_sim+eng', config=config)

            # 提取车牌候选
            candidates = extract_plate_candidates_from_text(text)
            for candidate in candidates:
                if validate_plate_number(candidate):
                    return candidate

        except Exception as e:
            print(f"直接识别方法{i+1}出错: {str(e)}")
            continue

    return None

def try_region_recognition(img):
    """
    尝试区域分割识别
    """
    print("尝试区域识别...")

    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    height, width = gray.shape

    # 定义多个区域
    regions = [
        (0, 0, width, height//2),           # 上半部分
        (0, height//2, width, height//2),  # 下半部分
        (0, height//3, width, height//3),  # 中间部分
        (width//4, 0, width//2, height),   # 中间垂直条
    ]

    for _, (x, y, w, h) in enumerate(regions):
        try:
            region = gray[y:y+h, x:x+w]

            # 预处理区域
            _, binary = cv2.threshold(region, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # OCR识别
            config = '--psm 6 --oem 3 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼'
            text = pytesseract.image_to_string(binary, lang='chi_sim+eng', config=config)

            # 提取车牌候选
            candidates = extract_plate_candidates_from_text(text)
            for candidate in candidates:
                if validate_plate_number(candidate):
                    return candidate

        except Exception:
            continue

    return None

def try_contour_recognition(img):
    """
    尝试传统的轮廓检测方法
    """
    print("尝试轮廓识别...")

    try:
        # 使用简化的轮廓检测
        processed_img, original_img = preprocess_image_simple(img)
        if processed_img is None:
            return None

        # 检测车牌区域
        plate_regions = detect_plate_regions_simple(processed_img, original_img)

        # 对每个区域进行识别
        for plate_img in plate_regions:
            plate_number = recognize_text_from_plate(plate_img)
            valid_plate = validate_plate_number(plate_number)
            if valid_plate:
                return valid_plate

    except Exception as e:
        print(f"轮廓识别出错: {str(e)}")

    return None

def extract_plate_candidates_from_text(text):
    """
    从文本中提取车牌候选
    """
    import re

    if not text:
        return []

    # 清理文本
    text = text.strip().replace(' ', '').replace('\n', '').replace('\t', '')

    candidates = []

    # 中国车牌正则表达式
    patterns = [
        r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][A-Z][A-Z0-9]{5}',  # 7位车牌
        r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][A-Z][A-Z0-9]{6}',  # 8位车牌
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text)
        candidates.extend(matches)

    return candidates

def preprocess_image_simple(img):
    """
    简化的图像预处理
    """
    try:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 自适应阈值
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

        return binary, img
    except:
        return None, None

def detect_plate_regions_simple(processed_img, original_img):
    """
    简化的车牌区域检测
    """
    try:
        # 查找轮廓
        contours, _ = cv2.findContours(processed_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        plate_regions = []

        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 500:  # 最小面积
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h

            # 宽松的车牌比例检查
            if 1.5 < aspect_ratio < 6.0 and w > 50 and h > 15:
                plate_img = original_img[y:y+h, x:x+w]
                if plate_img.size > 0:
                    plate_regions.append(plate_img)

        return plate_regions[:5]  # 最多返回5个候选区域
    except:
        return []

# 测试函数
def test_recognition(image_path):
    """
    测试车牌识别功能

    Args:
        image_path: 测试图像路径
    """
    print(f"\n开始测试图像: {image_path}")
    print("步骤1: 检测图像中是否存在车辆")
    try:
        from recognition.vehicle_detector import has_vehicle
        vehicle_detected = has_vehicle(image_path)
        if vehicle_detected:
            print("✓ 检测到车辆，继续进行车牌识别")
        else:
            print("✗ 未检测到车辆，停止车牌识别")
            return
    except ImportError:
        print("! 警告：车辆检测模块未找到，跳过车辆检测步骤")

    print("\n步骤2: 识别车牌")
    plate_number = recognize_plate_from_image(image_path)
    if plate_number:
        print(f"✓ 识别成功，车牌号: {plate_number}")
    else:
        print("✗ 未能识别车牌")
    print("测试完成\n")

if __name__ == "__main__":
    # 测试代码
    test_image = "test_plate.jpg"  # 替换为实际测试图像路径
    if os.path.exists(test_image):
        test_recognition(test_image)
    else:
        print(f"测试图像 {test_image} 不存在")

    # 可以添加更多测试图像
    # test_images = ["test_plate.jpg", "no_vehicle.jpg", "blurry_plate.jpg"]
    # for img in test_images:
    #     if os.path.exists(img):
    #         test_recognition(img)
    #     else:
    #         print(f"测试图像 {img} 不存在")
